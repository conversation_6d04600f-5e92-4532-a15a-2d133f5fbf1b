<script setup lang="ts">
import { ref, computed } from "vue";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import type { Publication } from "@/data/portfolio";
import { sectionTitles } from "@/data/portfolio";
import PublicationCard from "./PublicationCard.vue";

interface Props {
    sectionTitle: string;
    publications: Publication[];
}

interface TabConfig {
    value: string;
    label: string;
    filterFn: (publications: Publication[]) => Publication[];
    getCount: (publications: Publication[]) => number;
    getStatusText: (count: number) => string;
    triggerClass: string;
    sectionTitle?: string;
    sectionIcon: {
        color: string;
    };
    publicationType?: "published" | "unpublished";
}

const props = defineProps<Props>();

const activeTab = ref("all");

// Centralized tab configuration
const tabConfigs: TabConfig[] = [
    {
        value: "all",
        label: "All",
        filterFn: (publications) => publications,
        getCount: (publications) => publications.length,
        getStatusText: (count) => `Showing all ${count} paper${count !== 1 ? "s" : ""}.`,
        triggerClass:
            "data-[state=active]:bg-white data-[state=active]:text-slate-900 text-slate-600 hover:text-slate-900 transition-colors",
        sectionIcon: { color: "" }, // No icon for "all" tab
    },
    {
        value: "published",
        label: "Published",
        filterFn: (publications) => publications.filter((pub) => pub.type === "published"),
        getCount: (publications) => publications.filter((pub) => pub.type === "published").length,
        getStatusText: (count) => `Showing ${count} published paper${count !== 1 ? "s" : ""}.`,
        triggerClass:
            "data-[state=active]:bg-white data-[state=active]:text-sky-700 text-slate-600 hover:text-slate-900 transition-colors",
        sectionTitle: sectionTitles.published,
        sectionIcon: { color: "bg-sky-600" },
        publicationType: "published",
    },
    {
        value: "unpublished",
        label: "Unpublished",
        filterFn: (publications) => publications.filter((pub) => pub.type === "unpublished"),
        getCount: (publications) => publications.filter((pub) => pub.type === "unpublished").length,
        getStatusText: (count) => `Showing ${count} unpublished paper${count !== 1 ? "s" : ""}.`,
        triggerClass:
            "data-[state=active]:bg-white data-[state=active]:text-amber-700 text-slate-600 hover:text-slate-900 transition-colors",
        sectionTitle: sectionTitles.unpublished,
        sectionIcon: { color: "bg-amber-500" },
        publicationType: "unpublished",
    },
];

// Computed properties using the centralized configuration
const currentTabConfig = computed(() => tabConfigs.find((config) => config.value === activeTab.value) || tabConfigs[0]);

const filteredPublications = computed(() => currentTabConfig.value.filterFn(props.publications));

const publishedPapers = computed(() =>
    tabConfigs.find((config) => config.value === "published")!.filterFn(props.publications)
);

const unpublishedPapers = computed(() =>
    tabConfigs.find((config) => config.value === "unpublished")!.filterFn(props.publications)
);

const getStatusText = computed(() => currentTabConfig.value.getStatusText(filteredPublications.value.length));
</script>

<template>
    <section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
        <!-- Section Header -->
        <div class="border-l-4 border-sky-600 pl-4 mb-8">
            <h2 id="pub-title" class="text-2xl text-slate-700 font-normal">{{ props.sectionTitle }}</h2>
        </div>

        <!-- Filter Tabs -->
        <div class="mb-6">
            <p class="text-sm text-slate-500 mb-3">Filter:</p>

            <Tabs v-model="activeTab" class="w-full">
                <TabsList class="grid w-fit grid-cols-3 bg-slate-100 p-1">
                    <TabsTrigger
                        v-for="config in tabConfigs"
                        :key="config.value"
                        :value="config.value"
                        :class="config.triggerClass"
                    >
                        {{ config.label }} ({{ config.getCount(publications) }})
                    </TabsTrigger>
                </TabsList>

                <p class="text-sm text-slate-400 mt-3">{{ getStatusText }}</p>

                <!-- Dynamic Tab Content -->
                <TabsContent
                    v-for="config in tabConfigs"
                    :key="config.value"
                    :value="config.value"
                    :class="config.value === 'all' ? 'space-y-10 mt-6' : 'space-y-6 mt-6'"
                >
                    <!-- For "All" tab, show both published and unpublished sections -->
                    <template v-if="config.value === 'all'">
                        <!-- Published Papers Section -->
                        <div v-if="publishedPapers.length > 0">
                            <div class="flex items-center gap-2 mb-6">
                                <div class="w-3 h-3 bg-sky-600 rounded-full"></div>
                                <h3 class="text-lg text-slate-700 font-medium">Published</h3>
                            </div>
                            <div class="space-y-6">
                                <PublicationCard
                                    v-for="(publication, index) in publishedPapers"
                                    :key="publication.id"
                                    :publication="publication"
                                    :index="index"
                                    type="published"
                                />
                            </div>
                        </div>

                        <!-- Unpublished Papers Section -->
                        <div v-if="unpublishedPapers.length > 0">
                            <div class="flex items-center gap-2 mb-6">
                                <div class="w-3 h-3 bg-amber-500 rounded-full"></div>
                                <h3 class="text-lg text-slate-700 font-medium">Waiting list</h3>
                            </div>
                            <div class="space-y-6">
                                <PublicationCard
                                    v-for="(publication, index) in unpublishedPapers"
                                    :key="publication.id"
                                    :publication="publication"
                                    :index="index"
                                    type="unpublished"
                                />
                            </div>
                        </div>
                    </template>

                    <!-- For specific type tabs, show filtered publications -->
                    <template v-else>
                        <div
                            v-if="config.sectionTitle && config.sectionIcon.color"
                            class="flex items-center gap-2 mb-6"
                        >
                            <div :class="['w-3 h-3 rounded-full', config.sectionIcon.color]"></div>
                            <h3 class="text-lg text-slate-700 font-medium">{{ config.sectionTitle }}</h3>
                        </div>
                        <PublicationCard
                            v-for="(publication, index) in config.filterFn(publications)"
                            :key="publication.id"
                            :publication="publication"
                            :index="index"
                            :type="config.publicationType || 'published'"
                        />
                    </template>
                </TabsContent>
            </Tabs>
        </div>
    </section>
</template>
